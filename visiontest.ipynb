{"cells": [{"cell_type": "markdown", "id": "084aae5a", "metadata": {}, "source": ["从ModelScope下载模型到本地,也可以去终端用hf下"]}, {"cell_type": "code", "execution_count": 84, "id": "a5ae9415", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["huggingface连接失败\n"]}, {"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: modelscope in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (1.28.1)\n", "Requirement already satisfied: requests>=2.25 in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from modelscope) (2.32.4)\n", "Requirement already satisfied: setuptools in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from modelscope) (80.9.0)\n", "Requirement already satisfied: tqdm>=4.64.0 in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from modelscope) (4.67.1)\n", "Requirement already satisfied: urllib3>=1.26 in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from modelscope) (2.5.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from requests>=2.25->modelscope) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from requests>=2.25->modelscope) (3.10)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/miniconda3/envs/pyunsloth/lib/python3.11/site-packages (from requests>=2.25->modelscope) (2025.7.14)\n", "Note: you may need to restart the kernel to use updated packages.\n", "开始从魔塔社区下载模型\n", "Downloading Model from https://www.modelscope.cn to directory: gemma-3-4b-pt/unsloth/gemma-3-4b-pt\n", "Model downloaded to: gemma-3-4b-pt/unsloth/gemma-3-4b-pt\n", "==((====))==  Unsloth 2025.7.8: Fast Gemma3 patching. Transformers: 4.54.0.\n", "   \\\\   /|    NVIDIA GeForce RTX 4090. Num GPUs = 1. Max memory: 23.527 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 8.9. CUDA Toolkit: 12.6. Triton: 3.3.1\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = None. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 2/2 [00:29<00:00, 14.95s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["模型加载成功！\n"]}], "source": ["#第一步加载模型\n", "import os\n", "from unsloth import FastVisionModel\n", "import torch\n", "# 设置镜像源（清华源或阿里云）\n", "os.environ[\"HF_ENDPOINT\"]  = \"https://hf-mirror.com\" \n", "load_in_4bit = True \n", "model_name=\"unsloth/gemma-3-4b-pt\"\n", "try:\n", "    model, processor = FastVisionModel.from_pretrained(\n", "    model_name = model_name,\n", "    load_in_4bit = load_in_4bit, # Use 4bit to reduce memory use. False for 16bit LoRA.\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for long context\n", ")\n", "except:\n", "    print(\"huggingface连接失败\")\n", "    %pip install modelscope\n", "    print(\"开始从魔塔社区下载模型\")\n", "    from modelscope import snapshot_download\n", "    # 从ModelScope下载模型到本地\n", "    model_dir = snapshot_download(model_name, cache_dir='gemma-3-4b-pt')\n", "    print(f\"Model downloaded to: {model_dir}\")\n", "    model, processor = FastVisionModel.from_pretrained(\n", "        model_name=model_dir,  # 使用本地路径\n", "        load_in_4bit=load_in_4bit,\n", "        use_gradient_checkpointing = \"unsloth\",\n", "    )\n", "    print(\"模型加载成功！\")"]}, {"cell_type": "code", "execution_count": 85, "id": "78ebfccf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Making `base_model.model.model.vision_tower.vision_model` require gradients\n"]}], "source": ["model = FastVisionModel.get_peft_model(\n", "    model,\n", "    finetune_vision_layers     = True, # False if not finetuning vision layers\n", "    finetune_language_layers   = True, # False if not finetuning language layers\n", "    finetune_attention_modules = True, # False if not finetuning attention layers\n", "    finetune_mlp_modules       = True, # False if not finetuning MLP layers\n", "\n", "    r = 16,                           # The larger, the higher the accuracy, but might overfit\n", "    lora_alpha = 16,                  # Recommended alpha == r at least\n", "    lora_dropout = 0,\n", "    bias = \"none\",\n", "    random_state = 3407,\n", "    use_rslora = False,               # We support rank stabilized LoRA\n", "    loftq_config = None,               # And LoftQ\n", "    target_modules = \"all-linear\",    # Optional now! Can specify a list if needed\n", "    modules_to_save=[\n", "        \"lm_head\",\n", "        \"embed_tokens\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": 86, "id": "e40740b0", "metadata": {}, "outputs": [], "source": ["from unsloth import get_chat_template\n", "\n", "processor = get_chat_template(\n", "    processor,\n", "    \"gemma-3\"\n", ")"]}, {"cell_type": "code", "execution_count": 87, "id": "8561a494", "metadata": {}, "outputs": [], "source": ["from datasets import Dataset \n", "import json \n", " \n", "with open(\"unsloth/hlk/sft_dataset.json\")  as f:\n", "    data = json.load(f) \n", " \n", "# 直接创建数据集（无需保存） \n", "datasethlk = Dataset.from_dict({\"data\":  data})"]}, {"cell_type": "code", "execution_count": 88, "id": "1cee6b0b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Map:   0%|          | 0/49 [00:00<?, ? examples/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Map: 100%|██████████| 49/49 [00:00<00:00, 2026.03 examples/s]\n"]}], "source": ["from PIL import Image \n", "import os\n", " \n", "def process_example(example):\n", "    # 从嵌套的data字段中提取图片路径（注意路径拼接）\n", "    image_path = example[\"data\"][\"images\"][0]  # 例如: 'images/微机保护装置HLK-DPRI-1753859111524.png' \n", "    \n", "    # 假设图片基础路径需要拼接（根据实际路径调整）\n", "    base_path = \"unsloth/hlk/\"  # 替换为您的实际路径 \n", "    full_path = os.path.join(base_path,  image_path)\n", "    \n", "    # 打开图片并注入到pixel_values字段\n", "    example[\"pixel_values\"] = Image.open(full_path) \n", "    return example\n", " \n", "# 执行映射操作 \n", "datasethlk = datasethlk.map(process_example) "]}, {"cell_type": "code", "execution_count": 89, "id": "03880a87", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset({\n", "    features: ['data', 'pixel_values'],\n", "    num_rows: 49\n", "})\n", "{'conversations': [{'from': 'human', 'value': '<image>详细介绍一下这款产品？'}, {'from': 'gpt', 'value': '产品名称是户内压气式负荷开关 HLK-KNS，由湖南埃尔凯电器有限公司生产，HLK-KNS 户内压气式负荷开关及其组合电器是集成了隔离开关、负荷开关、接地开关、熔断器四种分立元件为一体的综合性高压电器产品。符合 IEC 62271.104、IEC 62271.105 的型式试验，按 IEC 507 的要求通过了盐雾条件下的绝缘试验，还委托中国国家高压电器检验测试中心按 IEC 最新标准及中国国家最新标准进行了全面型式试验，具有权威性。该产品开断分断电流能力强，进线端静触头具备隔离保护功能，在柜壳中或外的安装方式灵活多样，配用附件功能的品种齐全，安装使用简单可靠。'}], 'images': ['images/户内压气式负荷开关HLK-KNS-1753859406540.png']}\n", "<PIL.PngImagePlugin.PngImageFile image mode=RGB size=243x295 at 0x7A821B2F9A10>\n"]}], "source": ["print(datasethlk)  # 查看第一条数据的所有字段名\n", "print(datasethlk[0][\"data\"])\n", "print(datasethlk[0][\"pixel_values\"])"]}, {"cell_type": "code", "execution_count": 90, "id": "bc625144", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["详细介绍一下这款产品？\n", "产品名称是户内压气式负荷开关 HLK-KNS，由湖南埃尔凯电器有限公司生产，HLK-KNS 户内压气式负荷开关及其组合电器是集成了隔离开关、负荷开关、接地开关、熔断器四种分立元件为一体的综合性高压电器产品。符合 IEC 62271.104、IEC 62271.105 的型式试验，按 IEC 507 的要求通过了盐雾条件下的绝缘试验，还委托中国国家高压电器检验测试中心按 IEC 最新标准及中国国家最新标准进行了全面型式试验，具有权威性。该产品开断分断电流能力强，进线端静触头具备隔离保护功能，在柜壳中或外的安装方式灵活多样，配用附件功能的品种齐全，安装使用简单可靠。\n", "<PIL.PngImagePlugin.PngImageFile image mode=RGB size=243x295 at 0x7A822136ABD0>\n"]}], "source": ["example=datasethlk[0]\n", "human_msg = example[\"data\"][\"conversations\"][0][\"value\"].replace(\"<image>\", \"\").strip()\n", "gpt_msg = example[\"data\"][\"conversations\"][1][\"value\"]\n", "img_path = example[\"pixel_values\"]\n", "print(human_msg)\n", "print(gpt_msg)\n", "print(img_path)"]}, {"cell_type": "code", "execution_count": 91, "id": "d39c0920", "metadata": {}, "outputs": [], "source": ["def convert_to_conversation(sample):\n", "    human_msg = sample[\"data\"][\"conversations\"][0][\"value\"].replace(\"<image>\", \"\").strip()\n", "    gpt_msg = sample[\"data\"][\"conversations\"][1][\"value\"]\n", "    conversation = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\"type\": \"text\", \"text\": human_msg},\n", "                {\"type\": \"image\", \"image\": sample[\"pixel_values\"]},\n", "            ],\n", "        },\n", "        {\"role\": \"assistant\", \"content\": [{\"type\": \"text\", \"text\": gpt_msg}]},\n", "    ]\n", "    return {\"messages\": conversation}\n", "pass\n", "converted_datasethlk = [convert_to_conversation(sample) for sample in datasethlk]\n"]}, {"cell_type": "code", "execution_count": 92, "id": "15111143", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=243x295>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是户内压气式负荷开关 HLK-KNS，由湖南埃尔凯电器有限公司生产，HLK-KNS 户内压气式负荷开关及其组合电器是集成了隔离开关、负荷开关、接地开关、熔断器四种分立元件为一体的综合性高压电器产品。符合 IEC 62271.104、IEC 62271.105 的型式试验，按 IEC 507 的要求通过了盐雾条件下的绝缘试验，还委托中国国家高压电器检验测试中心按 IEC 最新标准及中国国家最新标准进行了全面型式试验，具有权威性。该产品开断分断电流能力强，进线端静触头具备隔离保护功能，在柜壳中或外的安装方式灵活多样，配用附件功能的品种齐全，安装使用简单可靠。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=224x423>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是微机保护装置HLK-DPRI，由湖南埃尔凯电器有限公司生产，HLK-DPRI微机保护装置主要是针对环网柜系统应用而开发。它具有 相间电流速断、过流、三种动作特性曲线的反时限过流保护、 零序过流 保护、重合闸、过电压、低电压保护及非电量保护跳闸功能，还具有多 电量测量、遥控、遥信等监控功能。HLK-DPR    I 继电保护装置采用高集 成度、总线不出芯片的微处理器处理来自电流、电压互感器的信号，通过数字逻辑运算控制装置的输出。装置结构紧凑，密封机箱，免维护设 计，抗干扰性能好，非常适合于运行环境较为恶劣、安装位置有限的环 网柜系统。产品主要技术特点：\\n·整机采用极低功耗设计技术，保证保护功能在任何条件下可靠快速启动。\\n· 装置结构简单小巧，安装方便灵活，适合环网柜的紧凑安装条。\\n·保护配置灵活齐全，各种保护功能均可以通过控制字自由投退。\\n· 三 种 IEC 标准反时限曲线选择的相间反时限过流保护。\\n· 具有完善的测控功能，可以测量电压、电流、有功功率、无功\\n·功率、视在功率、功率因数等电气参数；提供专门的遥控继电器实现遥控功能；具有7路开关量输入回路。\\n·采用全中文液晶显示界面，多层菜单显示，人机界面极为友好。\\n· 装置大容量的非易失存储器保证记录100 次历史事件记录，记录内容详细，掉电不丢失数据。\\n· 装置具备完善的动静态自检功能，在线监视装置各部分工作状况，保证了 装置的工作可靠性。\\n·高精度元件及工艺保证装置的精确性、可靠性及长久的使用寿。\\n· 装置提供 RS-485  通讯总线接口，并向用户提供开放的通讯协议，方便 实现 SCADA功能。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=239x270>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是微机保护装置 HLK-DPRII，由湖南埃尔凯电器有限公司生产，HLK-DPRⅡ 此款微机保护装置适用于 10kV 及以下电压等级电力系统、厂矿企业、市政工程、轨道交通等配、用电设备的综合保护、自动控制及测量。\\u200b\\n产品主要技术特点：\\u200b\\n保护配置灵活齐全，各种保护功能均可以通过控制字自由投退。\\u200b\\n具有完善的测控功能，可以测量电压、电流、有功功率、无功功率、视在功率、功率因数等电气参数；\\u200b\\n全封闭机箱，传统后接线设计，装置整体抗干扰能力强。\\u200b\\n采用全中文液晶显示界面，多层菜单显示，人机界面极为友好。\\u200b\\n记录 100 次历史事件记录，记录内容详细，掉电不丢失数据。\\u200b\\n提供 RS485 通讯总线接口，开放通讯协议，方便实现 SCADA 功能。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=218x328>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是低压电缆分支箱 DFW 型系列，由湖南埃尔凯电器有限公司生产，低压电缆分支箱 DFW 型多功能户外低压电缆分线箱适用于额定电压 380V、电流 800A 及以下的配电系统中，主要由断路器、母排、绝缘支撑件、壳体组成。\\u200b\\n该产品作为电缆终端负荷的现场电能分配和控制设备，能将电力电缆与箱变、负荷开关柜、环网供电单元等连结起来，起到分接、分支、中继或转换作用。其外壳采用 SMC（玻璃纤维增强聚脂）复合绝缘材料热压成型技术制造，箱体为封闭式结构，门框内装有密封胶条，在门关上后可增强防护等级，密封性好，广泛应用于各工矿企业、街道、城网电缆化改造、住宅区等户外、户内配电场所。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=244x263>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是户内高压真空负荷开关 HLK-ZNX，由湖南埃尔凯电器有限公司生产，HLK-ZNX 是该公司继承了欧美现今的真空开关技术而研制的新一代中压开关产品，应用真空灭弧的原理来实现 12kV 线路上过载及短路故障保护的高分断真空开关。随着世界各地用户环保意识的提高，环境友好型真空开关将逐步替代环境污染型产品。\\u200b\\nHLK-ZNX 是供应欧美及亚太地区的新型低碳开关设备，用于取代老式空气灭弧以及六氟化硫开关设备。其熔断器组合电器综合了真空灭弧室高分断能力，与熔断器组合电器在大电流短路时反时限速断的优势，既能如断路器（真空灭弧室）多次开断过载电流，降低开关运行成本，实现低碳运行，又可在破坏性冲击电流作用下，以 10ms 反时限速断（熔断器），确保变压器不受损伤。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=243x260>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是高压真空断路器 VQ1 (ZN63A)，由湖南埃尔凯电器有限公司生产，VQ1 (ZN63A) 型户内高压真空断路器（以下简称断路器）是用于 12kV 电力系统的户内开关设备，作为电网设备、工矿企业动力设备的保护和控制单元。断路器采用操作机构与断路器本体一体式设计，既可做为固定安装单元，也可配用专用推进机构，组成手车单元使用，其中固定式断路器可以增加相应的联锁，以满足配 XGN2、GGIA 等固定柜的需要。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=172x369>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是低压动力配电柜 XL-21，由湖南埃尔凯电器有限公司生产，XL-21 型低压动力柜适合于工业与民用建筑中作交流频率为 50HZ、电压 500V 及以下、三相四线电力系统的动力配电和照明配电用。\\u200b\\n同时，它适合制作各种电动机启动柜，包括全压直接启动柜，全数字控制软启动柜、自耦降压启动柜，星三角启动柜，多速电动机启动柜等。具有数十种一次线路方案号或派生方案号供选择使用，适用范围广。柜内用薄钢板弯制焊接而成，正面为单扇左开门，门上装有电压表、电流表等测量仪表和操作电器、信号电器等，是一种结构简单、运行可靠、成本较低的动力配电装置，具有配电方案灵活、组合方便、实用性强、结构新颖等特点。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=225x223>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是三相多表位计量箱，由湖南埃尔凯电器有限公司生产，三相多表位电能计量箱是电网公司采用集成化的设计理念，将电能表、控制开关等电力设备集装于一体化封闭箱内，不仅占用空间小，而且能够阻止窃电者触及计量装置，防止窃电行为。\\u200b\\n同时，该计量箱具有绝缘性能高、透气性能好、抗紫外线、耐老化、耐酸雨、耐盐雾、耐低温、耐高温、抗冲击等性能，并有良好的通风、防雨、散热功能，使用寿命达到 20 年以上，广泛用于城乡居民小区、楼宇住宅等用户用电的计量。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=227x291>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是 LK-PD 型应急照明配电箱，由湖南埃尔凯电器有限公司生产，应急照明配电箱（以下简称为配电箱）是在应急照明和疏散指示系统中为自带电源型消防应急灯具供电的供配电装置。\\u200b\\n当发生火灾时，该配电箱可通过人为或者自动方式进入应急状态，广泛应用于学校、商场、工厂、医院、宾馆、办公楼、住宅区等人员密集的场所。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=207x240>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是智能低压固定式开关柜 GDF，由湖南埃尔凯电器有限公司生产，GDF 型低压固定分隔式开关柜适用于三相交流 50、60Hz，额定工作电压 380V、660V，额定电流 6300A 及以下的三相四线制及三相五线制电力系统。\\u200b\\n该开关柜广泛应用于发电厂、变电站、工矿企业、大楼宾馆、机场、码头以及广播电视等通信中心，是输配电、电能转换及电能消耗的动力配电中的 PC 和电动机控制中心 MCC 的低压成套配置装置。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=313x298>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是低压成套抽屉式开关柜 GCK，由湖南埃尔凯电器有限公司生产，GCK 型低压抽出式开关柜（以下称装置）是联合设计组根据行业主管部门、广大电力用户及设计单位的要求设计研制出的符合国情、具有较高技术性指标、能够适应电力市场发展需要并可与现有引进产品竞争的低压抽出式开关柜。\\u200b\\n该装置适用于发电厂、石油、化工、冶金、纺织、高层建筑等行业的配电系统，在大型发电厂、石化系统等自动化程度高，要求与计算机接口的场所，可作为三相交流频率为 50（60）Hz、额定工作电压为 380V（400）、（600）、额定电流为 6300A 及以下的发、供电系统中的配电、电动机集中控制、无功率补偿使用的低压成套配电装置。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=266x290>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是低压成套抽屉式开关柜 GCS，由湖南埃尔凯电器有限公司生产，GCS 型低压抽出式开关柜（以下简称装置）是联合设计组根据行业主管部门、广大电力用户及设计单位的要求设计研制出的符合国情、具有较高技术性能指标、能够适应电力市场发展需要并可与现有引进产品竞争的低压抽出式开关柜。\\u200b该装置目前已被电力用户广泛选用。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=209x367>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是单相多表位计量箱，由湖南埃尔凯电器有限公司生产，单相多表位电能计量箱是电网公司采用集成化的设计理念，将电能表、控制开关等电力设备集装于一体化封闭箱内，不仅占用空间小，而且能够阻止窃电者触及计量装置，防止窃电行为。\\u200b\\n同时，该计量箱具有绝缘性能高、透气性能好、抗紫外线、耐老化、耐酸雨、耐盐雾、耐低温、耐高温、抗冲击等性能，并有良好的通风、防雨、散热功能，使用寿命达到 20 年以上，广泛用于城乡居民小区、楼宇住宅等用户用电的计量。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=313x293>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是标准化低压开关柜，由湖南埃尔凯电器有限公司生产，标准化低压开关柜是依据国网公司《低压开关柜标准化设计方案 (2020 版)》进行设计的产品。\\u200b\\n其典型结构方案共计 4 大类 8 小类：4 大类为进线柜、母联柜、馈线柜、无功功率补偿柜；8 小类为进线、母联柜各 1 类，馈线柜 4 类，无功功率补偿柜 2 类。该开关柜具有 “安全可靠、坚固耐用、标准统一、通用互换、合理分级、广泛适用” 的特点，简化了运维、提升了效率，为未来低压配电网智能化管理预留了接口。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=204x313>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是组合式照明箱 XPD，由湖南埃尔凯电器有限公司生产，组合式照明箱 XPD 适用于公寓、高层建筑、港口、车站、机场、仓库、厂矿企业及民建工程的建筑照明和小型动力控制电路。\\u200b\\n该照明箱适用于交流 50HZ，单相 240V、三相 415V 及以下，总电流 630A 及以下的户内照明和动力配电线路，可作为线路过载保护、适中保护以及线路切换等。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=304x255>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是低压成套抽屉式开关柜 MNS，由湖南埃尔凯电器有限公司生产，该装置在大单机容量的发电厂、大规模石化等行业的低压动力控制中心和电动机控制中心等电力使用场合，能满足与计算机接口的特殊需要。\\u200b\\n装置是根据电力部门、广大电力用户及设计部门的要求，为满足不断发展的电力市场对增容、计算机接口、动力集中控制、方便安装维修、缩短事故处理时间等需要，本着安全、经济、合理、可靠的原则设计的新型低压抽出式开关柜。产品具有分断、接通能力高、动热稳定性好、电气方案灵活、组合方便、系列性实用性强、结构新颖、防护等级高等特点，可以作为新型低压抽出式开关柜供用户选用。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=306x260>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是低压成套开关设备 GGD，由湖南埃尔凯电器有限公司生产，GGD 型交流低压配电柜适用于变电站、发电厂、厂矿企业等电力用户的交流 50Hz，额定工作电压 380V，额定工作电流 1000-3150A 的配电系统，作为动力、照明及发配电设备的电能转换、分配与控制之用。\\u200b\\nGGD 型交流低压配电柜是根据能源部，广大电力用户及设计部门的要求，按照安全、经济、合理、可靠的原则设计的新型低压配电柜。产品具有分断能力高，动热稳定性好，电气方案灵活、组合方便，系列性、实用性强、结构新颖，防护等级高等特点，可作为低压成套开关设备的更新换代产品使用。\\u200b\\n'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=157x230>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是剩余电流保护智能 (漏电) 重合闸断路器 HLKEL1C，由湖南埃尔凯电器有限公司生产，剩余电流保护智能 (漏电) 重合闸断路器采用高性能 32 位 ARM 微处理器，能实时进行信号处理和智能控制，实时监测跟踪线路剩余电流，自动调节档位，保证产品的投运率和可靠性。\\u200b\\n该断路器具有高分断能力，可保证线路短路保护的可靠性，且具备通信功能，能实现遥信、遥测、遥控、遥调。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=332x137>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是双电源 HLKEQ，由湖南埃尔凯电器有限公司生产，该自动转换开关具有体积小、结构简单的特点；操作方便，使用寿命长；开关切换驱动采用单电机驱动，运行平稳、无噪音、冲击力小；具有手动、自动切换功能；两台断路器之间具有可靠的机械联锁装置和电气联锁保护，彻底杜绝了两台断路器同时合闸的可能性。\\u200b\\n其智能化控制器采用以单片机为控制核心，硬件简洁，功能强大，扩展方便，可靠性高。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=156x233>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是塑壳断路器 HLKEM1，由湖南埃尔凯电器有限公司生产，塑料外壳式断路器具有过载、短路和欠电压保护功能，能保护线路和电源设备不受损坏，产品中增加三档延时可调功能分别为：0.8xIn、0.9xIn、1.0xIn。\\u200b\\n该断路器可垂直安装（即竖装），亦可水平安装（即横装），且具有隔离功能。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=404x311>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '全绝缘型环网开关设备 HRMX—12\\u200b\\n产品名称是全绝缘型环网开关设备 HRMX—12，由湖南埃尔凯电器有限公司生产，HRMX—12 系列全密封、全绝缘型环网开关设备（以下简称充气柜）是埃尔凯电力科技人员引入丹麦中压开关设备有限公司的核心技术、加入许多中国元素而创新开发的新一代小尺寸、模块化（SF6、N2、干燥空气）全绝缘开关设备。\\u200b\\n该设备采用超低阻值的真空灭弧室及自能式负荷开关，且一次带电部件全密封于由机器人焊接而成的充满环保气体不锈钢气箱内，免受外部环境影响，具有良好的可靠性、安全性、经济性、灵活性，传承了结构紧凑、免维护等特点。其广泛应用于城市配网、智能微电网、轨道交通、商业地产、市政工程、轻工业、住宅小区等领域，践行环保理念，为客户持续创造价值。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=165x423>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '直流屏 GZDW 系列\\u200b\\n产品名称是直流屏 GZDW 系列，由湖南埃尔凯电器有限公司生产，GZDW 系列直流操作电源系统是发电厂、水电站、变电站、开闭所等不可缺少的二次设备之一，它的可靠性直接影响到发电厂、变电站设备的安全可靠运行。\\u200b\\n经过近几年的发展，电力操作电源系统已经普遍采用了阀控密封铅酸蓄电池组和高频开关电源模块组成的充电装置。其中，高频开关电源模块具有转换效率高、功率密度高、稳压范围宽、噪音低、稳压稳流精度高、纹波系数小、可靠性高、配置灵活等特点，是新一代直流操作电源的主体。直流操作电源还包含一个独立的监控系统，主要完成对系统的测量、控制，并对所测量到的数据进行运算、判断，产生各种信号，如报警、显示、控制输出等动作，其核心任务是监视并保证系统的安全运行，完成蓄电池的智能管理。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=420x275>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '一二次融合环保气体成套环网箱\\u200b\\n产品名称是一二次融合环保气体成套环网箱，由湖南埃尔凯电器有限公司生产，一二次融合成套环网箱是由环网柜（含进出线单元、电压互感器柜）、站所终端、外箱体、连接电缆等构成。\\u200b\\n该开关设备具有模块化、可扩展、全绝缘、全密封、安全可靠、免维护、无线测温、在线监测装置等优点。各进出线单元的三相电流（保护 / 测量）和零序电流信号，提供母线三相测量电压和零序电压信号，以及二次设备工作电源和开关操作电源。DTU 单元整体实现三遥、计量、相间及接地故障处理、通信、二次供电等功能。其适用于任何恶劣环境，被广泛应用于工业园区、居民区、街道、机场、各种建筑物、繁华商业中心等场所。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=222x407>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '固体全绝缘环网开关设备\\u200b\\n产品名称是固体全绝缘环网开关设备，由湖南埃尔凯电器有限公司生产，HXGN-12 系列固体全绝缘封闭式环网开关设备是一种全绝缘、全密封、免维护的固体绝缘真空开关设备。其高压带电部分均用绝缘性能优良的环氧树脂材料浇注成型，将真空灭弧室、主导电回路、绝缘支撑等有机结合为一整体，功能单元通过全绝缘固体汇流母线连接，因此，整个开关设备不受外部环境影响，可保障设备运行的可靠性和操作人员的安全性。\\u200b\\n该环网柜具有结构简单、操作灵活、联锁可靠、安装方便等特点，适用于 50Hz、12 千伏的电力系统，广泛应用于工业及民用电缆环网及配网终端工程，作为电能的接受和分配之用，特别适用于城市居民区配电、小型变电站、开闭所、电缆分支箱、箱式变电站、工矿企业、商场、机场、地铁、风力发电、医院、体育场、铁路、隧道等场所使用。\\u200b\\n由于该产品具有全绝缘、全密封、全屏蔽的优点，所以特别适用于高海拔、高温、湿热、严寒、污染严重等环境恶劣的地区使用。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=292x729>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是气体绝缘金属封闭开关设备 HCCS-40.5，由湖南埃尔凯电器有限公司生产，该设备全部采用 SF6 气体作为绝缘介质，并将所有的高压电器元件封闭在接地的金属箱体内，它将断路器、隔离开关、接地开关、电压互感器、电流互感器、避雷器、母线、电缆终端、进出线套管等，经优化设计有机地组合成一个整体而成的高压配电装置，国际上称为 “气体绝缘开关设备”（Gas Insulated Switchgear），简称 GIS。\\u200b\\nHCCS-40.5 系列气体绝缘金属封闭开关设备是该公司引进丹麦的核心技术进行国产化设计改进，全面合作研制的新一代 C-GIS 产品，柜内高压电器组件有断路器、隔离开关及其操作机构、互感器、高压熔断器、套管等。其主要在变电站和配电站中使用，并且可完成用户变电站的开关任务，这些开关柜额定电压高达 40.5kV，额定电流高达 2500A，适用于 100kA 的最大允许额定短路电流和 40kA 的最大短路分断电流。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=384x384>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '箱型固定式户内交流金属封闭开关设备\\u200b\\n产品名称是箱型固定式户内交流金属封闭开关设备，由湖南埃尔凯电器有限公司生产，HLK-HXGN-12 箱型固定式交流金属封闭开关设备用于 12kV 及以下三相交流 50Hz 系统中作为接受与分配电能之用，特别适合于频繁操作的场合，其母线系统为单母线，也可派生出单母线带旁路和双母线结构。\\u200b\\n该产品符合国家标准 GB3906《3-35kV 交流金属封闭开关设备》、电力部 DL/T404《户内交流高压开关柜订货技术条件》的要求，也满足国际标准 IEC60298《1kV 以上 52kV 以下交流金属封闭开关设备和控制设备》，并且具有 “五防” 闭锁功能。开关柜采用金属封闭式结构，柜体骨架由角钢焊接而成，内部用钢板严密分隔成母线室、断路器室、进出线电缆室、继电保护、仪表室及控制小室五个独立的间隔室，整个柜体由三部分组成。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=463x227>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是智能化箱式变电站，由湖南埃尔凯电器有限公司生产，它由高压单元、变压器单元、低压单元组成，是作为接受和分配电能并通过智慧供配电物联网对电路实行控制、保护及检测的装置。后台软件系统主要监控对象有配电开关、门禁系统、温度、湿度、漏水、震动、视频集成等。\\u200b\\n在任何时间任何地方都能轻松的用手机了解箱式变电站的运行数据、状态情况及周围动态环境。当高压电源周围环境中存在气压、烟道气体、蒸汽、水浸以及固体撞击、强振动和热源烘烤温度等 5 要素可能会对开关设备、操作者的生命造成安全隐患时，由视频监控或智能传感器通过动态识别直接发送预警信号到后台运维系统。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=338x799>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是交流充电桩，由湖南埃尔凯电器有限公司生产，交流充电俗称 “慢充”，其设备固定安装在电动汽车外、与交流电网连接，国标采用 220V 单相交流电，是为电动汽车车载充电机（即固定安装在电动汽车上的充电机）提供交流电源的供电装置。交流充电桩只提供电力输出，没有充电功能，需连接车载充电机为电动汽车充电。\\n产品细分\\n壁挂式单枪交流充电桩：有 7kW、11kW、22kW 规格，节省空间，适合安装在墙壁等空间受限场所。\\n立式单枪交流充电桩：包括 7kW、14kW、42kW 规格，安装稳固，可放置在停车场等开阔区域。\\n立式双枪交流充电桩：有 14kW、84kW 规格，可同时为两辆车辆充电，提高充电效率。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=363x561>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是直流充电桩，由湖南埃尔凯电器有限公司生产，直流充电，也就是俗称的 “快充”，其核心设备直流充电桩固定安装在电动汽车外部，并与交流电网相连，专门为非车载电动汽车的动力电池提供直流电源。该充电桩输入电压采用三相四线 AC380V ±15%（即日常所说的接 380V 电），频率为 50Hz，输出则是可调节的直流电，能直接为电动汽车的动力电池充电。得益于三相四线制供电，直流充电桩可提供充足功率，输出电压和电流的调整范围大，充分满足快速充电需求。面向社会乘用车辆时，辅助电源电压为 12V ，一般情况下，车辆电池从 0 充至满电需 1 - 2 小时，但实际时长会因不同车型的动力电池容量及实际充电功率有所差异。\\u200b\\n产品细分\\u200b\\n壁挂式：有 20kW、30kW 直流充电桩，这类充电桩较为节省空间，适合安装在空间有限的场所，如部分地下停车场的墙壁处，为周边车辆提供便捷的快充服务。\\u200b\\n立式：功率涵盖 30kW、40kW、60kW、80kW、120kW、160kW、180kW、240kW、360kW 等多种规格。立式充电桩稳固且显眼，可独立放置于各类充电站、停车场空旷区域，适配不同功率需求的电动汽车。\\u200b\\n分体多枪式：包括 180kW 分体 4 枪、200kW 分体 5 枪、240kW 分体 4 枪及分体 12 枪、300kW 分体 5 枪、360kW 分体 6 枪、400kW 分体 10 枪、480kW 分体 8 枪及分体 10 枪和分体 12 枪、600kW 分体 10 枪及分体 15 枪、900kW 分体 15 枪等多种型号。分体多枪式充电桩可同时为多辆电动汽车充电，极大提高了充电效率，尤其适用于车流量大、充电需求集中的大型公共充电站，能有效减少车辆排队等待时间，提升整体运营效益。\\u200b\\n不同规格的直流充电桩凭借各自特性，广泛应用于公共充电站、高速公路服务区、住宅小区停车场、商业中心停车场等各类场所，为电动汽车用户提供高效、便捷的充电服务，有力推动了电动汽车行业的发展。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=412x301>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是标准化箱变 - 紧凑型，由湖南埃尔凯电器有限公司生产，公司生产的标准化箱变依据国网公司《10 千伏高压低压预装式变电站标准化设计方案 (2023 版)》进行设计，紧凑型是其中一种设计方案，并通过了中国电科院审查及苏科院的型式试验检验。\\n标准化箱变具有安全可靠、坚固耐用、标准统一、通用互换的特点，提升了箱变的运维便利性。其由三个相对独立的隔室组成，即高压开关柜室、变压器室、低压开关柜室等，彼此独立又相互配合，在各自介质的连接下，将高电压转化为所需的稳定低电压，供生活、生产使用。\\n国网标准化箱变中，系统额定频率为 50Hz，高压侧电压为 10kV，低压侧电压为 0.4kV。典型方案方面，高压单元可采用 SF6 气体绝缘、环保气体绝缘、固体绝缘环网柜、常压密封空气绝缘环网柜组成终端型和环网型供电方案；变压器单元选用节能环保型、全密封、油浸式变压器（硅钢片 / 非晶合金）；自动化终端为 DTU 公共单元独立二次柜；低压柜单元为柜式组屏，4-6 路出线回路灵活配置，630A 以下电流灵活选择；无功补偿装置采用一体式智能电容，补偿容量按额定容量的 15% 配置；箱体外形长度为 3600mm，宽度为 2200mm，高度不大于 2500mm。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=444x247>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是高压智能化开闭所 HLK-XGW-12kV，由湖南埃尔凯电器有限公司生产，HLK-XGW-12kV 系列高压智能化开闭所由环进环出单元、馈线单元、母线设备（TV）单元、集中式 DTU 单元、外箱体、连接电缆等构成。实现四遥（遥控、遥测、遥调、遥信）功能，满足配电网络自动化系统的要求。适用于交流 50Hz、12/24kV 的配电网络中，作为电能的接受和分配之用。可根据不同的设计方案任意连接组合，完成不同的配电任务。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=422x270>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是高低压预装式变电站 YB-12/0.4，由湖南埃尔凯电器有限公司生产，它是一种将高压开关设备、配电变压器和低压配电装置按一定接线方案排成一体的工厂预制户内、户外紧凑式配电设备，即将高压受电、变压器降压、低压配电等功能有机组合在一起，安装在防潮、防锈、防尘、防鼠、防火、防盗、隔热、全封闭、可移动的钢结构箱体内，机电一体化，全封闭运行，特别适用于城网建设与改造，替代了原有的土建配电房、配电站，具有技术先进安全可靠、自动化程度高、工厂预制化、组合方式灵活、投资省、见效快、占地面积小等优点，适用于矿山、工厂企业、油气田和风力发电站等场所。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=371x230>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是光伏预装式变电站 YB□-40.5，由湖南埃尔凯电器有限公司生产，YB 系列华式箱式变电站是集升压变压器、高压熔断器、高压油浸式负荷开关、低压开关及相应辅助配套设备于一体的专用电力设备。它能将太阳能经光伏组件、逆变器转化输出的 0.27kV～0.4kV 电压升高到 35kV 后，经 35kV 电缆线路向上输出电能，是光伏发电系统的理想配套设备。\\u200b\\nYB 系列华式箱式变电站应用于 50HZ、35kV/0.27kV-0.8kV 的光伏发电系统，变压器的额定容量为 630kVA-6300kVA，专用于光伏发电厂供配电。其具有供电可靠、结构合理、安装迅速、灵活、操作方便、体积小等卓越性能，广泛用于工业园区、居民小区、商业中心、城市街道、高层建筑、风力发电、光伏发电等各种场所。该类产品与目前国内生产的欧式箱变不同在于：华式箱变是将变压器铁心、高压负荷开关、保护用熔断器等设备一体化设计、放置于同一油箱中，因而体积小。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=343x337>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是高压开关机械健康状态智能监测装置，由湖南埃尔凯电器有限公司生产，该装置由公司完全自主研发，对高压开关关键机械部件：2 个合闸弹簧、3 个分闸弹簧、3 个真空灭弧室触头压簧的疲劳状态，A、B、C 三相开关接触部位 6 路梅花触头的温升，储能传动机构凸轮轴的转动位置，合 / 分闸机构的半轴位置等健康状态进行实时检测，使用触摸屏设置、修改参数和实时显示监测数据与状态，通过 3 种可选用的通信方式与远程监控平台构成通信网络，定时或者在远程监控平台查询时实时向平台上传装置的监测数据和状态，在诊断出装置发生故障时，能及时进行本地报警，将故障信息通过通信方式上报远程监控平台进行处理，不仅能提高高压开关柜的运行可靠性，而且能进一步确保配供电的安全。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=402x300>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是标准化箱变 - 替代型，由湖南埃尔凯电器有限公司生产，公司生产的标准化箱变依据国网公司《10 千伏高压低压预装式变电站标准化设计方案 (2023 版)》进行设计，替代型是其中一种设计方案，并通过了中国电科院审查及苏科院的型式试验检验。该箱变具有安全可靠、坚固耐用、标准统一、通用互换的特点，提升了运维便利性，由高压开关柜室、变压器室、低压开关柜室等三个相对独立的隔室组成，彼此独立又相互配合，将高电压转化为稳定的低电压供生活生产使用。系统额定频率 50Hz，高压侧电压 10kV，低压侧电压 0.4kV，典型方案中高压单元、变压器单元、自动化终端、低压柜单元、无功补偿装置等配置与标准型、紧凑型一致，箱体外形尺寸同样为长度 3600mm、宽度 2200mm、高度不大于 2500mm。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=371x491>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '湖南埃尔凯电器有限公司生产的 HRMXE-V-12/630-20 系列环保气体绝缘环网柜，将环保气体作为主绝缘介质充入全密封气箱结构，是新型金属封闭开关设备。其核心单元采用发明专利的自能式负荷开关、超低阻值真空断路器，一次带电部件封装于充满环保气体的微正压不锈钢气箱内，与外界隔离，具备绿色环保、环境适应性强、安全可靠、维护量少、体积小、模块化和标准化程度高等优势，广泛应用于配电网建设改造的电缆供电系统，能有效提高供电可靠性、增强人员安全性，满足经济稳定运行需求。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGBA size=369x320>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '机械联锁式中压双电源快速自动切换电气装置由湖南埃尔凯电器有限公司生产，该装置重新研发了一套单独的机械联锁，克服了电气联锁中因为电气故障而导致联锁失效的问题，通过纯机械闭锁，实现两路开关不能同时合闸的技术要求，具有高可靠性，高安全性及高耐用性。\\n该装置的机械联锁和电气联锁加备自投装置为主体，实现安全可靠的双进线自动转换。对于有一级负荷用电场所（双电源供电），从物理角度上完全杜绝了移开中置式开关在合闸通电位置的双电源同时合闸的误操作。同时还能保证双电源互相切换做出毫米级快速响应，实现瞬时切换。其适用于数据中心、大型楼宇、智能电网、医院、交通、机场、指挥中心及冶金化工等对供电可靠性要求较高的场所 。在实际应用场景中，当主供电源突然失电时，备供电源会通过备自投装置自动切换供电，同时机械联锁系统动作，确保供电的安全性和连续性。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=171x458>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '交流铠装移开中置式金属封闭开关设备 KYN28A-12（Z）由湖南埃尔凯电器有限公司生产，是 3.6 - 12kV 三相交流 50HZ 单母线及单母线分段系统的成套配电装置。其分三层结构，上层为母线和仪表室（相互隔离），中间层为断路器室，下层为电缆室。\\n该设备主要用于发电厂厂用电输变电系统变电所的受配电、工矿企业事业配电等场合，可对电路实行控制、保护和监测，具备完善的 “五防” 联锁功能，能有效防止误操作及误入带电间隔，确保人身和设备安全。柜体采用敷铝锌板经多重折弯组装而成，完全铠装及全封闭，柜内隔室相互独立。其同型号手车互换性良好，二次线敷设于线槽内，便于检修。从正面进行安装调试和维护，可背靠背组成双重排列或靠墙安装，能减少占地面积。同时，采用通用附件和标准备品备件，降低了维护成本 。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=308x405>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是铠装移开式交流金属封闭开关柜 KYN61-40.5 (Z)，由湖南埃尔凯电器有限公司生产，KYN61-40.5 (Z) 是三相交流 50Hz、额定电压 40.5kV 的户内成套配电装置，作为发电厂、变电站及工矿企业接受和分配电能之用，对电路起到控制、保护和检测等功能，还可用于频繁操作的场所。其柜体结构采用组装式、断路器采用手车落地式结构；配用全新型复合绝缘真空断路器，且有良好的互换性；手车车架中装有丝杠螺母推进机构，可轻松移动手车，并防止误操作而损坏推进机构；主开关、手车、开关柜门之间的联锁均采用强制机械闭锁方式，满足 “五防” 联锁系统；合理的散热通道确保开关柜抗燃弧同时满足安全运行。产品通过了国家权威试验站的型式试验认证，根据客户要求，最高可适用于海拔 3500 米，可配合智能化控制单元监控开关柜各元件运行参数，适用于三相交流 3.6~12kV 及 40.5kV 单母线及单母线分段电力系统，用于接受和分配电能并对电路实行控制、保护及监测。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=196x133>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是微型漏电断路器HLKEB8L，由湖南埃尔凯电器有限公司生产，HLKEB8L-63系列小型漏电断路器适用于交流50Hz（或60Hz）、额定电压400V及单相230V，额定电流自6A至63A的线路中，具有漏电触电、过载、短路等保护功能，额定剩余动作电流30mA的漏电断路器可对人身触电提供直接保护。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=119x255>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是高分段断路器 HLKEB8N，由湖南埃尔凯电器有限公司生产，HLKEB8N-32 系列高分段断路器适用于交流 50Hz、额定电压 230V 及以下的单相住宅线路中，对电气线路的过载和短路进行保护。该产品分断能力高、体积小、宽度仅为 18mm。零、火线同时切断，杜绝了火线、零线接反或零线对地电位造成的人身及火灾危险，是目前使用在住宅领域中最理想的配电保护开关。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=161x202>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是小型断路器 HLKEB8-63，由湖南埃尔凯电器有限公司生产，HLKEB8-63 小型断路器具有结构先进合理、性能可靠、分断能力高、外型美观小巧等特点，壳体等部件采用耐冲击、高阻燃材料构成。适用于交流 50Hz 或 60Hz，额定工作电压 400V 以下，额定电流自 3A 至 63A 以下的场所。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=257x268>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是智能型万能式断路器 HLKEW1，由湖南埃尔凯电器有限公司生产，HLKE-W1 系列智能型万能式断路器适用于交流 50Hz，额定工作电压至 690V 及以下，额定电流 400A-6300A 的配电网络中，用来分配电能和保护线路及电源设备免受过载、欠电压、短路、单相接地及漏电影响。该断路器具有智能化保护功能，有较高精度的选择性保护，能提高供电可靠性，同时带有标准的 RS485 通讯接口，可进行 “遥测”“遥讯”“遥控”“遥调” 四遥功能，以满足集群控制中心和自动化系统的要求。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=180x262>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是电子式塑壳断路器 HLKEM1Z，由湖南埃尔凯电器有限公司生产，电子式塑壳断路器具有过载长延时反时限、断路短延时反时限、断路短延时定时限、断路瞬时和欠电压保护功能，能保护线路和电流设备不受损坏。断路器可垂直安装（即竖装），亦可水平安装（即横装）。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=194x205>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是高分断小型断路器 HLKEM6-125，由湖南埃尔凯电器有限公司生产，HLKEM6-125 系列高分断小型断路器具有外表美观小巧、重量轻、性能优良可靠，分断能力较高，脱扣迅速，导轨安装，壳体和部件采用高阻燃及耐冲塑料，使用寿命长，主要用于交流 50Hz/60Hz 单极、230V/400V；额定电流 63A 至 125A 线路中作过载、短路保护，同时也可以使用在频繁地通断电器装置和照明线路中。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=157x153>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是隔离开关HLKEB8G，由湖南埃尔凯电器有限公司生产，HLKEB8G系列隔离开关适用于交流50Hz/60Hz、额定电压230V/400V以下、额定电流100A的电路中，主要供无负载情况下接通或断开电路，作线路与电源接通或隔离之用，尤其适合线路检修时有效隔离电源并防止意外合闸，以确保检修人员的操作安全，广泛使用在国家农网改造透明电表箱配套产品。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=174x212>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是漏电断路器 HLKEB8NL-32，由湖南埃尔凯电器有限公司生产，HLKEB8NL-32 系列漏电断路器适用于交流 50Hz 或 60Hz，额定电压 230V 的单相住宅线路中，作为人身触电保护之用，并对民用电气线路的过载和短路进行保护。该产品具有体积小，分断能力高；火线同时切断，并在火线接反的情况下，仍能对人身触电进行保护。本产品亦可根据用户要求，增加过压保护功能。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=394x250>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是标准化箱变 - 标准型，由湖南埃尔凯电器有限公司生产，公司生产的标准化箱变依据国网公司《10 千伏高压低压预装式变电站标准化设计方案 (2023 版》进行设计，标准型是其中一种设计方案，并通过了中国电科院审查及苏科院的型式试验检验。标准化箱变具有安全可靠，坚固耐用、标准统一、通用互换的特点，提升了箱变的运维便利性。其由三个相对独立的隔室组成，即高压开关柜室、变压器室、低压开关柜室等，它们彼此独立又相互配合，在各自介质的连接下，把高电压转化成所需要的稳定的低电压以供生活、生产使用。国网标准化箱变中，系统的额定频率为 50Hz，高压侧电压为 10kV，低压侧电压为 0.4kV。典型方案中，高压单元可采用多种绝缘环网柜组成终端型和环网型供电方案；变压器单元选用节能环保型、全密封、油浸式变压器；自动化终端为 DTU 公共单元独立二次柜；低压柜单元为柜式组屏，4-6 路出线回路灵活配置，630A 以下电流灵活选择；无功补偿装置采用一体式智能电容，补偿容量按照额定容量的 15% 进行配置；箱体外形长度为 3600mm，宽度为 2200mm，高度不大于 2500mm。'}]}]},\n", " {'messages': [{'role': 'user',\n", "    'content': [{'type': 'text', 'text': '详细介绍一下这款产品？'},\n", "     {'type': 'image',\n", "      'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=178x207>}]},\n", "   {'role': 'assistant',\n", "    'content': [{'type': 'text',\n", "      'text': '产品名称是剩余电流保护塑壳断路器 HLKEL1，由湖南埃尔凯电器有限公司生产，带剩余电流保护塑壳断路器具有过载、短路和欠电压保护功能，能保护线路和电源设备不受损坏；同时，可提供接触保护，还可以对过电流保护不能检测出的长期存在的接地故障可能引起的火灾危险提供保护。该断路器具有体积小、分断能力高、飞弧短、抗振动等特点。'}]}]}]"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["converted_datasethlk"]}, {"cell_type": "code", "execution_count": 93, "id": "63567de5", "metadata": {}, "outputs": [], "source": ["from unsloth.trainer import UnslothVisionDataCollator\n", "from trl import SFTTrainer, SFTConfig\n", "\n", "FastVisionModel.for_training(model) # Enable for training!\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    train_dataset=converted_datasethlk,\n", "    processing_class=processor.tokenizer,\n", "    data_collator=UnslothVisionDataCollator(model, processor),\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 1,\n", "        gradient_accumulation_steps = 4,\n", "        gradient_checkpointing = True,\n", "\n", "        # use reentrant checkpointing\n", "        gradient_checkpointing_kwargs = {\"use_reentrant\": False},\n", "        max_grad_norm = 0.3,              # max gradient norm based on QLoRA paper\n", "        warmup_ratio = 0.03,\n", "        max_steps = 30,\n", "        #num_train_epochs = 2,          # Set this instead of max_steps for full training runs\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        save_strategy=\"steps\",\n", "        optim = \"adamw_torch_fused\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"cosine\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\",             # For Weights and Biases\n", "\n", "        # You MUST put the below items for vision finetuning:\n", "        remove_unused_columns = False,\n", "        dataset_text_field = \"\",\n", "        dataset_kwargs = {\"skip_prepare_dataset\": True},\n", "        max_seq_length = 2048,\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 95, "id": "042847e1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 49 | Num Epochs = 3 | Total steps = 30\n", "O^O/ \\_/ \\    Batch size per device = 1 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (1 x 4 x 1) = 4\n", " \"-____-\"     Trainable parameters = 38,497,792 of 4,338,577,264 (0.89% trained)\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='30' max='30' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [30/30 02:03, Epoch 2/3]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>3.783400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>3.341400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>3.397200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>2.926000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>3.200400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>3.164500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>2.715100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>2.780900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>2.584000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>2.345300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>2.403300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>2.510500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.805900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.978000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>2.593100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.952600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>2.034900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.949100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>2.307000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>2.313900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>2.380700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.873200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>1.907200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>2.080100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>2.496600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.861400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>1.980300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>2.356800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>2.098200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.948700</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": 100, "id": "a21583e1", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGB size=243x295>"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["datasethlk[0][\"pixel_values\"]"]}, {"cell_type": "code", "execution_count": 99, "id": "a1dc8276", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["​\n", "湖南埃尔凯电器有限公司生产的 400kV 隔离开关（下称“隔离开关”）是经湖南埃尔凯电器有限公司自研生产的固定式交流无功补偿发电厂 400kV 供电系统的开关设备之一，主要用作线路的断、联、合，作为供电系统的保护装置，以及将电网与发电厂的电力系统进行互联、互换，并可作为母线开关使用。\n", "\n", "\n", "隔离开关由隔离开关本体、联锁装置、控制电源装置、操作装置、测量装置和辅助\n"]}], "source": ["FastVisionModel.for_inference(model)  # Enable for inference!\n", "\n", "image = datasethlk[0][\"pixel_values\"]\n", "instruction = \"详细介绍一下这款产品？\"\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [{\"type\": \"image\"}, {\"type\": \"text\", \"text\": instruction}],\n", "    }\n", "]\n", "\n", "input_text = processor.apply_chat_template(messages, add_generation_prompt=True)\n", "inputs = processor(\n", "    image,\n", "    input_text,\n", "    add_special_tokens=False,\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "\n", "text_streamer = TextStreamer(processor, skip_prompt=True)\n", "result = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                        use_cache=True, temperature = 1.0, top_p = 0.95, top_k = 64)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pyun<PERSON>loth", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}