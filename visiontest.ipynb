#第一步加载模型
import os
from unsloth import FastVisionModel
import torch
# 设置镜像源（清华源或阿里云）
os.environ["HF_ENDPOINT"]  = "https://hf-mirror.com" 
load_in_4bit = False  # 改为False，不使用量化
model_name="google/gemma-3-4b-it"  # 使用原始的非量化模型
try:
    model, processor = FastVisionModel.from_pretrained(
    model_name = model_name,
    load_in_4bit = load_in_4bit, # 不使用量化
    torch_dtype = torch.float16,  # 使用16bit精度而不是4bit量化
    use_gradient_checkpointing = "unsloth", # True or "unsloth" for long context
)
except:
    print("huggingface连接失败")
    %pip install modelscope
    print("开始从魔塔社区下载模型")
    from modelscope import snapshot_download
    # 从ModelScope下载模型到本地
    model_dir = snapshot_download(model_name, cache_dir='gemma-3-4b-pt')
    print(f"Model downloaded to: {model_dir}")
    model, processor = FastVisionModel.from_pretrained(
        model_name=model_dir,  # 使用本地路径
        load_in_4bit=load_in_4bit,
        use_gradient_checkpointing = "unsloth",
    )
    print("模型加载成功！")

model = FastVisionModel.get_peft_model(
    model,
    finetune_vision_layers     = True, # False if not finetuning vision layers
    finetune_language_layers   = True, # False if not finetuning language layers
    finetune_attention_modules = True, # False if not finetuning attention layers
    finetune_mlp_modules       = True, # False if not finetuning MLP layers

    r = 16,                           # The larger, the higher the accuracy, but might overfit
    lora_alpha = 16,                  # Recommended alpha == r at least
    lora_dropout = 0,
    bias = "none",
    random_state = 3407,
    use_rslora = False,               # We support rank stabilized LoRA
    loftq_config = None,               # And LoftQ
    target_modules = "all-linear",    # Optional now! Can specify a list if needed
    modules_to_save=[
        "lm_head",
        "embed_tokens",
    ],
)

from unsloth import get_chat_template

processor = get_chat_template(
    processor,
    "gemma-3"
)

from datasets import Dataset 
import json 
 
with open("unsloth/hlk/sft_dataset.json")  as f:
    data = json.load(f) 
 
# 直接创建数据集（无需保存） 
datasethlk = Dataset.from_dict({"data":  data})

from PIL import Image 
import os
 
def process_example(example):
    # 从嵌套的data字段中提取图片路径（注意路径拼接）
    image_path = example["data"]["images"][0]  # 例如: 'images/微机保护装置HLK-DPRI-1753859111524.png' 
    
    # 假设图片基础路径需要拼接（根据实际路径调整）
    base_path = "unsloth/hlk/"  # 替换为您的实际路径 
    full_path = os.path.join(base_path,  image_path)
    
    # 打开图片并注入到pixel_values字段
    example["pixel_values"] = Image.open(full_path) 
    return example
 
# 执行映射操作 
datasethlk = datasethlk.map(process_example) 

print(datasethlk)  # 查看第一条数据的所有字段名
print(datasethlk[0]["data"])
print(datasethlk[0]["pixel_values"])

example=datasethlk[0]
human_msg = example["data"]["conversations"][0]["value"].replace("<image>", "").strip()
gpt_msg = example["data"]["conversations"][1]["value"]
img_path = example["pixel_values"]
print(human_msg)
print(gpt_msg)
print(img_path)

def convert_to_conversation(sample):
    human_msg = sample["data"]["conversations"][0]["value"].replace("<image>", "").strip()
    gpt_msg = sample["data"]["conversations"][1]["value"]
    conversation = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": human_msg},
                {"type": "image", "image": sample["pixel_values"]},
            ],
        },
        {"role": "assistant", "content": [{"type": "text", "text": gpt_msg}]},
    ]
    return {"messages": conversation}
pass
converted_datasethlk = [convert_to_conversation(sample) for sample in datasethlk]


converted_datasethlk

from unsloth.trainer import UnslothVisionDataCollator
from trl import SFTTrainer, SFTConfig

FastVisionModel.for_training(model) # Enable for training!

trainer = SFTTrainer(
    model=model,
    train_dataset=converted_datasethlk,
    processing_class=processor.tokenizer,
    data_collator=UnslothVisionDataCollator(model, processor),
    args = SFTConfig(
        per_device_train_batch_size = 1,
        gradient_accumulation_steps = 4,
        gradient_checkpointing = True,

        # use reentrant checkpointing
        gradient_checkpointing_kwargs = {"use_reentrant": False},
        max_grad_norm = 0.3,              # max gradient norm based on QLoRA paper
        warmup_ratio = 0.03,
        max_steps = 30,
        #num_train_epochs = 2,          # Set this instead of max_steps for full training runs
        learning_rate = 2e-4,
        logging_steps = 1,
        save_strategy="steps",
        optim = "adamw_torch_fused",
        weight_decay = 0.01,
        lr_scheduler_type = "cosine",
        seed = 3407,
        output_dir = "outputs",
        report_to = "none",             # For Weights and Biases

        # You MUST put the below items for vision finetuning:
        remove_unused_columns = False,
        dataset_text_field = "",
        dataset_kwargs = {"skip_prepare_dataset": True},
        max_seq_length = 2048,
    )
)

trainer_stats = trainer.train()

datasethlk[0]["pixel_values"]

FastVisionModel.for_inference(model)  # Enable for inference!

image = datasethlk[0]["pixel_values"]
instruction = "详细介绍一下这款产品？"

messages = [
    {
        "role": "user",
        "content": [{"type": "image"}, {"type": "text", "text": instruction}],
    }
]

input_text = processor.apply_chat_template(messages, add_generation_prompt=True)
inputs = processor(
    image,
    input_text,
    add_special_tokens=False,
    return_tensors="pt",
).to("cuda")

from transformers import TextStreamer

text_streamer = TextStreamer(processor, skip_prompt=True)
result = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,
                        use_cache=True, temperature = 1.0, top_p = 0.95, top_k = 64)

